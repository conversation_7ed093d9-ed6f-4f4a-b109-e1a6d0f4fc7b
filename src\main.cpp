﻿#include <iostream>
#include "person.pb.h" // File header sinh bởi protoc

int main() {
    // Khởi tạo đối tượng Person
    tutorial::Person person;
    person.set_id(123);                  // Thiết lập trường id
    person.set_name("<PERSON><PERSON><PERSON>");   // Thiết lập tên
    person.set_email("<EMAIL>"); // Thiết lập email

    // Serialize đối tượng sang chuỗi nhị phân
    std::string output;
    if (!person.SerializeToString(&output)) {
        std::cerr << "Lỗi khi serialize Person." << std::endl;
        return -1;
    }

    // Giả sử lưu xuống file hoặc gửi qua mạng...

    // Giả lập đọc lại: tạo đối tượng mới và parse
    tutorial::Person person2;
    if (!person2.ParseFromString(output)) {
        std::cerr << "Lỗi khi parse Person." << std::endl;
        return -1;
    }

    // Hi<PERSON>n thị thông tin đã đọc
    std::cout << "ID: " << person2.id() << std::endl;
    std::cout << "Name: " << person2.name() << std::endl;
    std::cout << "Email: " << person2.email() << std::endl;

    return 0;
}