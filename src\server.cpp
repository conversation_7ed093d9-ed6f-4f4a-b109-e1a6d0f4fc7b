﻿#include <iostream>
#include <boost/asio.hpp>
#include "message.pb.h"

using boost::asio::ip::udp;

int main() {
    boost::asio::io_context io_context;
    udp::socket socket(io_context, udp::endpoint(udp::v4(), 9000));
    std::cout << "[Server] Listening at Port 9000...\n";

    while (true) {
        char data[1024];
        udp::endpoint sender_endpoint;
        size_t length = socket.receive_from(boost::asio::buffer(data), sender_endpoint);

        example::ChatMessage msg;
        if (msg.ParseFromArray(data, static_cast<int>(length))) {
            std::cout << "[Server] Recieve message "
                << sender_endpoint.address().to_string()
                << ":" << sender_endpoint.port()
                << " -> " << msg.text() << std::endl;
        }
        else {
            std::cerr << "[Server] Error: Can not parse message!" << std::endl;
        }
    }

    return 0;
}