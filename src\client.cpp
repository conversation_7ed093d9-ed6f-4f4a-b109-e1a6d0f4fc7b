#include <iostream>
#include <boost/asio.hpp>
#include "message.pb.h"

using boost::asio::ip::udp;

int main() {
    boost::asio::io_context io_context;
    udp::socket socket(io_context);
    socket.open(udp::v4());

    // Sử dụng make_address và cast port rõ ràng để tránh ambigous overload
    auto server_address = boost::asio::ip::make_address("127.0.0.1");
    unsigned short server_port = static_cast<unsigned short>(9000);
    udp::endpoint server_endpoint(server_address, server_port);

    std::cout << "[Client] Input message (Push Enter to send):\n";
    std::string line;
    while (std::getline(std::cin, line)) {
        example::ChatMessage msg;
        msg.set_text(line);

        std::string out;
        msg.SerializeToString(&out);

        socket.send_to(boost::asio::buffer(out), server_endpoint);
        std::cout << "[Client] Send message: " << line << std::endl;
    }

    return 0;
}