﻿cmake_minimum_required(VERSION 3.15)
project(proto_example)

# Chỉ định C++17
enable_language(CXX)
set(CMAKE_CXX_STANDARD 17)

# Đường dẫn đến vcpkg toolchain (nếu dùng vcpkg)
# set(CMAKE_TOOLCHAIN_FILE "${CMAKE_SOURCE_DIR}/toolchains/vcpkg.cmake")

# Tìm protobuf đã cài qua vcpkg hoặc hệ thống
find_package(Protobuf REQUIRED)

# Sinh code C++ từ .proto
protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${CMAKE_SOURCE_DIR}/person.proto)

# Thêm target
add_executable(proto_example src/main.cpp ${PROTO_SRCS} ${PROTO_HDRS})

# Liên kết thư viện protobuf
target_link_libraries(proto_example PRIVATE protobuf::libprotobuf)
