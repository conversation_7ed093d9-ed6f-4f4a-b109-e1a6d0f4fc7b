﻿cmake_minimum_required(VERSION 3.10)
project(udp_proto_boostasio)

# cmake -B build -A Win32 -DCMAKE_TOOLCHAIN_FILE=C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake

find_package(Boost REQUIRED COMPONENTS system)
find_package(Protobuf REQUIRED)

set(PROTO_DIR "${CMAKE_SOURCE_DIR}/proto")

protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${PROTO_DIR}/message.proto)

include_directories(
    ${Boost_INCLUDE_DIRS}
    ${Protobuf_INCLUDE_DIRS}
    ${CMAKE_CURRENT_BINARY_DIR}  
)

add_executable(server src/server.cpp ${PROTO_SRCS} ${PROTO_HDRS})
target_link_libraries(server PRIVATE
    Boost::system
    protobuf::libprotobuf       
)

target_link_libraries(server PRIVATE ${CMAKE_DL_LIBS})

add_executable(client src/client.cpp ${PROTO_SRCS} ${PROTO_HDRS})
target_link_libraries(client PRIVATE
    Boost::system
    protobuf::libprotobuf
)