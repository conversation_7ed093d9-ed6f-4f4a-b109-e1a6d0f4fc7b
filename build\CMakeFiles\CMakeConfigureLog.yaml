
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.2+c078802d4 for .NET Framework
      Build started 17/06/2025 7:19:56 CH.
      
      Project "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /analyze- /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx86\\x86\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.27
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/Works/proto_example/build/CMakeFiles/3.31.0-rc2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.2+c078802d4 for .NET Framework
      Build started 17/06/2025 7:19:57 CH.
      
      Project "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /analyze- /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx86\\x86\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Works\\proto_example\\build\\CMakeFiles\\3.31.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.02
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Works/proto_example/build/CMakeFiles/3.31.0-rc2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-1nriaq"
      binary: "D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-1nriaq"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:X86"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-1nriaq'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f1826.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.2+c078802d4 for .NET Framework
        Build started 17/06/2025 7:19:59 CH.
        
        Project "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\cmTC_f1826.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f1826.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\".
          Creating directory "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f1826.dir\\Debug\\\\" /Fd"cmTC_f1826.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x86
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f1826.dir\\Debug\\\\" /Fd"cmTC_f1826.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\cmTC_f1826.exe" /INCREMENTAL /ILK:"cmTC_f1826.dir\\Debug\\cmTC_f1826.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-1nriaq/Debug/cmTC_f1826.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-1nriaq/Debug/cmTC_f1826.lib" /MACHINE:X86  /machine:X86 cmTC_f1826.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_f1826.vcxproj -> D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\cmTC_f1826.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\cmTC_f1826.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\cmTC_f1826.write.1u.tlog" "cmTC_f1826.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' is not recognized as an internal or external command,
          operable program or batch file.
          The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\cmTC_f1826.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\cmTC_f1826.write.1u.tlog" "cmTC_f1826.dir\\Debug\\vcpkg.applocal.log"" exited with code 9009.
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\Debug\\cmTC_f1826.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\cmTC_f1826.write.1u.tlog" "cmTC_f1826.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\unsuccessfulbuild".
          Touching "cmTC_f1826.dir\\Debug\\cmTC_f1826.tlog\\cmTC_f1826.lastbuildstate".
        Done Building Project "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1nriaq\\cmTC_f1826.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.05
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x86/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x86/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-49ofvh"
      binary: "D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-49ofvh"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:X86"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-49ofvh'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f8a96.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.2+c078802d4 for .NET Framework
        Build started 17/06/2025 7:20:00 CH.
        
        Project "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\cmTC_f8a96.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f8a96.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\".
          Creating directory "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x86-windows" from "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\CL.exe /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f8a96.dir\\Debug\\\\" /Fd"cmTC_f8a96.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x86
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f8a96.dir\\Debug\\\\" /Fd"cmTC_f8a96.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\cmTC_f8a96.exe" /INCREMENTAL /ILK:"cmTC_f8a96.dir\\Debug\\cmTC_f8a96.ilk" /NOLOGO /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib" /LIBPATH:"C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-49ofvh/Debug/cmTC_f8a96.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Works/proto_example/build/CMakeFiles/CMakeScratch/TryCompile-49ofvh/Debug/cmTC_f8a96.lib" /MACHINE:X86  /machine:X86 cmTC_f8a96.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_f8a96.vcxproj -> D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\cmTC_f8a96.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\cmTC_f8a96.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\cmTC_f8a96.write.1u.tlog" "cmTC_f8a96.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' is not recognized as an internal or external command,
          operable program or batch file.
          The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\cmTC_f8a96.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\cmTC_f8a96.write.1u.tlog" "cmTC_f8a96.dir\\Debug\\vcpkg.applocal.log"" exited with code 9009.
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\Users\\<USER>\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\Debug\\cmTC_f8a96.exe" "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\debug\\bin" "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\cmTC_f8a96.write.1u.tlog" "cmTC_f8a96.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\unsuccessfulbuild".
          Touching "cmTC_f8a96.dir\\Debug\\cmTC_f8a96.tlog\\cmTC_f8a96.lastbuildstate".
        Done Building Project "D:\\Works\\proto_example\\build\\CMakeFiles\\CMakeScratch\\TryCompile-49ofvh\\cmTC_f8a96.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.04
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x86/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x86/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
