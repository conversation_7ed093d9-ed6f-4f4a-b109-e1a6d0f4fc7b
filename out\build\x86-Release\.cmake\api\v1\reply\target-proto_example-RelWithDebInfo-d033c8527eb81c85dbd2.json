{"artifacts": [{"path": "proto_example.exe"}, {"path": "proto_example.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "target_link_libraries", "set_target_properties", "include", "_find_package", "find_package"], "files": ["C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt", "C:/Users/<USER>/vcpkg/installed/x86-windows/share/protobuf/protobuf-targets.cmake", "C:/Users/<USER>/vcpkg/installed/x86-windows/share/protobuf/protobuf-config.cmake", "C:/Users/<USER>/vcpkg/installed/x86-windows/share/protobuf/vcpkg-cmake-wrapper.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 18, "parent": 0}, {"command": 0, "file": 0, "line": 598, "parent": 1}, {"command": 2, "file": 1, "line": 21, "parent": 0}, {"command": 6, "file": 1, "line": 12, "parent": 0}, {"command": 4, "file": 0, "line": 847, "parent": 4}, {"file": 4, "parent": 5}, {"command": 5, "file": 4, "line": 3, "parent": 6}, {"file": 3, "parent": 7}, {"command": 4, "file": 3, "line": 14, "parent": 8}, {"file": 2, "parent": 9}, {"command": 3, "file": 2, "line": 70, "parent": 10}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++17 -MD"}], "defines": [{"backtrace": 3, "define": "ABSL_CONSUME_DLL"}, {"backtrace": 3, "define": "PROTOBUF_USE_DLLS"}], "includes": [{"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/vcpkg/installed/x86-windows/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0, 1]}], "id": "proto_example::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:X86 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\libprotobuf.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Users\\<USER>\\vcpkg\\installed\\x86-windows\\lib\\abseil_dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "proto_example", "nameOnDisk": "proto_example.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}, {"name": "CMake Rules", "sourceIndexes": [3]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "isGenerated": true, "path": "out/build/x86-Release/person.pb.cc", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "out/build/x86-Release/person.pb.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "out/build/x86-Release/person.pb.h.rule", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}