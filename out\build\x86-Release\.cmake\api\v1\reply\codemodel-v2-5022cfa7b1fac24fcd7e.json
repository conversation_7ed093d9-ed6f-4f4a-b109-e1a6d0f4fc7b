{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "proto_example", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "proto_example::@6890427a1f51a3e7e1df", "jsonFile": "target-proto_example-RelWithDebInfo-d033c8527eb81c85dbd2.json", "name": "proto_example", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Works/proto_example/out/build/x86-Release", "source": "D:/Works/proto_example"}, "version": {"major": 2, "minor": 7}}